<!DOCTYPE html>
<html>

<head>
    <title>Test Data Loading</title>
</head>

<body>
    <h1>Data Loading Test</h1>
    <div id="output"></div>

    <script src="data.js"></script>
    <script src="script.js"></script>
    <script>
        console.log('navigationData:', navigationData);
        document.getElementById('output').innerHTML =
            '<p>Data loaded: ' + (typeof navigationData !== 'undefined') + '</p>' +
            '<p>Categories: ' + (navigationData ? navigationData.categories.length : 'N/A') + '</p>';

        // Test content generation
        if (typeof navigationData !== 'undefined' && navigationData.categories) {
            document.getElementById('output').innerHTML += '<p>Testing content generation...</p>';
            const testDiv = document.createElement('div');
            testDiv.innerHTML = '<main><div id="test-main"></div></main>';
            document.body.appendChild(testDiv);

            // Try to generate content
            try {
                generateMainContent(navigationData.categories);
                document.getElementById('output').innerHTML += '<p>Content generation successful!</p>';
            } catch (e) {
                document.getElementById('output').innerHTML += '<p>Error: ' + e.message + '</p>';
            }
        }
    </script>
</body>

</html>