// Load navigation data by including the data.js script
function loadNavigationData() {
  return new Promise((resolve) => {
    if (typeof navigationData !== 'undefined') {
      resolve(navigationData);
      return;
    }

    const script = document.createElement('script');
    script.src = 'data.js';
    script.onload = () => {
      resolve(navigationData);
    };
    script.onerror = () => {
      console.error('Failed to load navigation data');
      resolve(null);
    };
    document.head.appendChild(script);
  });
}

// Generate navigation menu
function generateNavigation(categories) {
  const navUl = document.querySelector('nav ul');
  navUl.innerHTML = '';

  categories.forEach(category => {
    const li = document.createElement('li');
    const a = document.createElement('a');
    a.href = `#${category.id}`;
    a.textContent = category.title;
    li.appendChild(a);
    navUl.appendChild(li);
  });
}

// Generate main content
function generateMainContent(categories) {
  const main = document.querySelector('main');
  main.innerHTML = '';

  categories.forEach(category => {
    // Create category title
    const title = document.createElement('h2');
    title.className = 'category-title';
    title.id = category.id;
    title.textContent = category.title;
    main.appendChild(title);

    // Create link grid
    const section = document.createElement('section');
    section.className = 'link-grid';

    category.links.forEach((link, index) => {
      const card = document.createElement('div');
      card.className = 'link-card';
      card.style.animationDelay = `${index * 0.05}s`;

      card.innerHTML = `
        <a href="${link.url}" target="_blank" rel="noopener noreferrer"></a>
        <i class="${link.icon}" aria-hidden="true"></i>
        <h3>${link.name}</h3>
      `;

      section.appendChild(card);
    });

    main.appendChild(section);
  });
}

// Search functionality
let allLinks = [];

function initializeSearch(categories) {
  // Flatten all links for search
  allLinks = categories.flatMap(category =>
    category.links.map(link => ({
      ...link,
      categoryId: category.id,
      categoryTitle: category.title
    }))
  );

  const searchInput = document.getElementById('searchInput');
  const searchBtn = document.getElementById('searchBtn');

  function performSearch() {
    const query = searchInput.value.toLowerCase().trim();

    if (query === '') {
      showAllCategories();
      return;
    }

    const filteredLinks = allLinks.filter(link =>
      link.name.toLowerCase().includes(query) ||
      link.url.toLowerCase().includes(query) ||
      link.categoryTitle.toLowerCase().includes(query)
    );

    displaySearchResults(filteredLinks, query);
  }

  searchInput.addEventListener('input', performSearch);
  searchBtn.addEventListener('click', performSearch);
  searchInput.addEventListener('keypress', (e) => {
    if (e.key === 'Enter') {
      performSearch();
    }
  });
}

function displaySearchResults(links, query) {
  const main = document.querySelector('main');
  main.innerHTML = '';

  if (links.length === 0) {
    main.innerHTML = `
      <div class="search-results">
        <h2 class="category-title">搜索结果</h2>
        <p style="text-align: center; color: #888; margin: 2rem 0;">
          没有找到包含 "${query}" 的网站
        </p>
      </div>
    `;
    return;
  }

  const title = document.createElement('h2');
  title.className = 'category-title';
  title.textContent = `搜索结果 (${links.length})`;
  main.appendChild(title);

  const section = document.createElement('section');
  section.className = 'link-grid';

  links.forEach((link, index) => {
    const card = document.createElement('div');
    card.className = 'link-card';
    card.style.animationDelay = `${index * 0.05}s`;

    card.innerHTML = `
      <a href="${link.url}" target="_blank" rel="noopener noreferrer"></a>
      <i class="${link.icon}" aria-hidden="true"></i>
      <h3>${link.name}</h3>
      <small style="color: #888; font-size: 0.7rem;">${link.categoryTitle}</small>
    `;

    section.appendChild(card);
  });

  main.appendChild(section);
}

function showAllCategories() {
  loadNavigationData().then(data => {
    if (data && data.categories) {
      generateMainContent(data.categories);
    }
  });
}

// Theme management
function initializeTheme() {
  const themeToggle = document.getElementById('themeToggle');
  const currentTheme = localStorage.getItem('theme') || 'dark';

  document.documentElement.setAttribute('data-theme', currentTheme);
  updateThemeIcon(currentTheme);

  themeToggle.addEventListener('click', () => {
    const themes = ['dark', 'light', 'high-contrast'];
    const currentIndex = themes.indexOf(document.documentElement.getAttribute('data-theme'));
    const nextTheme = themes[(currentIndex + 1) % themes.length];

    document.documentElement.setAttribute('data-theme', nextTheme);
    localStorage.setItem('theme', nextTheme);
    updateThemeIcon(nextTheme);
  });
}

function updateThemeIcon(theme) {
  const icon = document.querySelector('#themeToggle i');
  const icons = {
    'dark': 'fa-moon',
    'light': 'fa-sun',
    'high-contrast': 'fa-adjust'
  };

  icon.className = `fa-solid ${icons[theme]}`;
}

// Favorites management
function initializeFavorites() {
  const favoritesToggle = document.getElementById('favoritesToggle');
  let favorites = JSON.parse(localStorage.getItem('favorites') || '[]');

  favoritesToggle.addEventListener('click', () => {
    showFavorites();
  });

  // Add click handlers to link cards for favoriting
  document.addEventListener('click', (e) => {
    if (e.target.closest('.link-card') && e.ctrlKey) {
      e.preventDefault();
      const card = e.target.closest('.link-card');
      const link = card.querySelector('a');
      const name = card.querySelector('h3').textContent;
      const icon = card.querySelector('i').className;

      const favoriteItem = {
        name,
        url: link.href,
        icon
      };

      const existingIndex = favorites.findIndex(fav => fav.url === favoriteItem.url);

      if (existingIndex > -1) {
        favorites.splice(existingIndex, 1);
        showNotification('已从收藏夹移除');
      } else {
        favorites.push(favoriteItem);
        showNotification('已添加到收藏夹');
      }

      localStorage.setItem('favorites', JSON.stringify(favorites));
      updateFavoritesIcon();
    }
  });

  updateFavoritesIcon();
}

function showFavorites() {
  const favorites = JSON.parse(localStorage.getItem('favorites') || '[]');
  const main = document.querySelector('main');
  main.innerHTML = '';

  const title = document.createElement('h2');
  title.className = 'category-title';
  title.textContent = `我的收藏 (${favorites.length})`;
  main.appendChild(title);

  if (favorites.length === 0) {
    main.innerHTML += `
      <p style="text-align: center; color: #888; margin: 2rem 0;">
        还没有收藏任何网站<br>
        <small>按住 Ctrl 键点击网站卡片即可收藏</small>
      </p>
    `;
    return;
  }

  const section = document.createElement('section');
  section.className = 'link-grid';

  favorites.forEach((link, index) => {
    const card = document.createElement('div');
    card.className = 'link-card';
    card.style.animationDelay = `${index * 0.05}s`;

    card.innerHTML = `
      <a href="${link.url}" target="_blank" rel="noopener noreferrer"></a>
      <i class="${link.icon}" aria-hidden="true"></i>
      <h3>${link.name}</h3>
    `;

    section.appendChild(card);
  });

  main.appendChild(section);
}

function updateFavoritesIcon() {
  const favorites = JSON.parse(localStorage.getItem('favorites') || '[]');
  const button = document.getElementById('favoritesToggle');

  if (favorites.length > 0) {
    button.classList.add('active');
  } else {
    button.classList.remove('active');
  }
}

function showNotification(message) {
  const notification = document.createElement('div');
  notification.className = 'notification';
  notification.textContent = message;
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--primary-color);
    color: #000;
    padding: 1rem;
    border-radius: 8px;
    z-index: 10000;
    animation: slideIn 0.3s ease;
  `;

  document.body.appendChild(notification);

  setTimeout(() => {
    notification.remove();
  }, 3000);
}

// Navigation and smooth scrolling functionality
document.addEventListener("DOMContentLoaded", async function () {
  // Load and generate content from data
  const data = await loadNavigationData();
  if (data && data.categories) {
    generateNavigation(data.categories);
    generateMainContent(data.categories);
    initializeSearch(data.categories);
  }

  // Initialize features
  initializeTheme();
  initializeFavorites();

  const navLinks = document.querySelectorAll("nav a");

  // Initialize navigation
  initializeNavigation();
  
  // Add smooth scrolling to navigation links
  navLinks.forEach(function (link) {
    link.addEventListener("click", function (e) {
      e.preventDefault();
      
      // Remove active class from all links
      navLinks.forEach((l) => l.classList.remove("active"));
      
      // Add active class to clicked link
      this.classList.add("active");
      
      // Get target element
      const targetId = this.getAttribute("href").split("#")[1];
      const targetElement = document.getElementById(targetId);
      
      if (targetElement) {
        // Smooth scroll to target
        targetElement.scrollIntoView({ 
          behavior: "smooth",
          block: "start"
        });
        
        // Update URL without page reload
        const newUrl = `${window.location.protocol}//${window.location.host}${window.location.pathname}#${targetId}`;
        window.history.pushState({ path: newUrl }, "", newUrl);
      }
    });
  });

  // Handle hash changes (back/forward buttons)
  function handleHashChange() {
    const hash = window.location.hash;
    if (hash) {
      const targetElement = document.getElementById(hash.substring(1));
      if (targetElement) {
        targetElement.scrollIntoView({ 
          behavior: "smooth",
          block: "start"
        });
        
        // Update active navigation link
        const activeLink = document.querySelector(`nav a[href="${hash}"]`);
        if (activeLink) {
          navLinks.forEach((l) => l.classList.remove("active"));
          activeLink.classList.add("active");
        }
      }
    }
  }

  // Initialize navigation state
  function initializeNavigation() {
    // Set first nav item as active if no hash
    if (!window.location.hash && navLinks.length > 0) {
      navLinks[0].classList.add("active");
    } else {
      handleHashChange();
    }
  }

  // Listen for hash changes
  window.addEventListener("hashchange", handleHashChange);
  
  // Add loading animation delay for cards
  const linkCards = document.querySelectorAll('.link-card');
  linkCards.forEach((card, index) => {
    card.style.animationDelay = `${index * 0.05}s`;
  });
  
  // Add keyboard navigation support
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Tab') {
      // Ensure focus is visible for keyboard users
      document.body.classList.add('keyboard-navigation');
    }
  });
  
  document.addEventListener('mousedown', function() {
    document.body.classList.remove('keyboard-navigation');
  });
});

// Performance optimization: Preload critical resources
function preloadCriticalResources() {
  // Preload Font Awesome CSS
  const fontAwesome = document.createElement('link');
  fontAwesome.rel = 'preload';
  fontAwesome.as = 'style';
  fontAwesome.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css';
  document.head.appendChild(fontAwesome);
}

// Call preload function
preloadCriticalResources();
