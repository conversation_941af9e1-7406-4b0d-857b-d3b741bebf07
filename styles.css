:root {
  --primary-color: #ff9000;
  --bg-color: #0d0d0d;
  --card-bg-color: #1a1a1a;
  --text-color: #fff;
  --hover-bg-color: #2a2a2a;
  --transition-speed: 0.3s;
}

/* Light theme */
[data-theme="light"] {
  --primary-color: #ff6b00;
  --bg-color: #f5f5f5;
  --card-bg-color: #ffffff;
  --text-color: #333;
  --hover-bg-color: #f0f0f0;
}

/* High contrast theme */
[data-theme="high-contrast"] {
  --primary-color: #ffff00;
  --bg-color: #000000;
  --card-bg-color: #111111;
  --text-color: #ffffff;
  --hover-bg-color: #222222;
}

html {
  font-size: 16px;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  margin: 0;
  padding: 0;
  background-color: var(--bg-color);
  color: var(--text-color);
  line-height: 1.6;
}

header {
  background-color: #000;
  padding: 1rem;
  text-align: center;
  position: relative;
}

header h1 {
  font-size: 2rem;
  font-weight: bold;
  color: var(--primary-color);
  margin: 0 0 1rem 0;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.search-container {
  max-width: 400px;
  margin: 0 auto 1rem;
  position: relative;
  display: flex;
  align-items: center;
}

#searchInput {
  width: 100%;
  padding: 0.75rem 3rem 0.75rem 1rem;
  border: 2px solid var(--card-bg-color);
  border-radius: 25px;
  background-color: var(--card-bg-color);
  color: var(--text-color);
  font-size: 1rem;
  outline: none;
  transition: all var(--transition-speed) ease;
}

#searchInput:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(255, 144, 0, 0.1);
}

#searchInput::placeholder {
  color: #888;
}

#searchBtn {
  position: absolute;
  right: 0.5rem;
  background: none;
  border: none;
  color: var(--primary-color);
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all var(--transition-speed) ease;
}

#searchBtn:hover {
  background-color: var(--primary-color);
  color: #000;
}

.header-controls {
  position: absolute;
  top: 1rem;
  right: 1rem;
  display: flex;
  gap: 0.5rem;
}

.header-controls button {
  background: none;
  border: 2px solid var(--card-bg-color);
  color: var(--text-color);
  padding: 0.5rem;
  border-radius: 50%;
  cursor: pointer;
  transition: all var(--transition-speed) ease;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-controls button:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.header-controls button.active {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: #000;
}

nav {
  background-color: var(--card-bg-color);
  padding: 0.5rem;
  position: sticky;
  top: 0;
  z-index: 1000;
  backdrop-filter: blur(10px);
}

nav ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
}

nav li {
  margin: 0.3rem;
}

nav a {
  color: var(--text-color);
  text-decoration: none;
  font-size: 1rem;
  font-weight: bold;
  padding: 0.5rem 0.8rem;
  border-radius: 1.25rem;
  transition: all var(--transition-speed) ease;
  display: block;
}

nav a:hover,
nav a.active {
  background-color: var(--primary-color);
  color: #000;
  transform: translateY(-1px);
}

main {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
}

.category-title {
  font-size: 1.5rem;
  font-weight: bold;
  margin: 2rem 0 1rem;
  color: var(--primary-color);
  border-left: 4px solid var(--primary-color);
  padding-left: 0.6rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.link-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(8rem, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.link-card {
  background-color: var(--card-bg-color);
  border-radius: 12px;
  padding: 1.2rem 0.8rem;
  text-align: center;
  transition: all var(--transition-speed) ease;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 90px;
  position: relative;
  overflow: hidden;
  border: 1px solid transparent;
}

.link-card a {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  text-decoration: none;
  z-index: 12;
  display: block;
}

.link-card:hover {
  background-color: var(--hover-bg-color);
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(255, 144, 0, 0.15);
  border-color: var(--primary-color);
}

.link-card i {
  font-size: 1.8rem;
  margin-bottom: 0.7rem;
  color: var(--primary-color);
  transition: all var(--transition-speed) ease;
  position: relative;
  z-index: 11;
}

.link-card:hover i {
  transform: scale(1.15);
  filter: brightness(1.2);
}

.link-card h3 {
  font-size: 0.9rem;
  margin: 0;
  color: var(--text-color);
  line-height: 1.3;
  font-weight: 500;
  max-width: 100%;
  height: 2.6em;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  white-space: normal;
  padding: 0 0.3rem;
  position: relative;
  z-index: 11;
}

footer {
  background-color: #000;
  color: #ccc;
  text-align: center;
  padding: 1rem;
  font-size: 0.75rem;
  margin-top: 2rem;
}

footer nav {
  margin-top: 0.6rem;
  background-color: transparent;
}

footer nav a {
  color: #ccc;
  margin: 0 0.6rem;
  font-size: 0.75rem;
  transition: color var(--transition-speed) ease;
}

footer nav a:hover {
  color: var(--primary-color);
}

/* Loading animation */
.link-card {
  animation: fadeInUp 0.6s ease forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Notification styles */
.notification {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  font-weight: 500;
}

/* Search results styles */
.search-results {
  text-align: center;
  padding: 2rem;
}

/* Accessibility improvements */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--primary-color);
  color: #000;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 10000;
  font-weight: bold;
}

.skip-link:focus {
  top: 6px;
}

.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Keyboard navigation support */
.keyboard-navigation *:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* Focus styles for interactive elements */
button:focus,
input:focus,
a:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --primary-color: #ffff00;
    --bg-color: #000000;
    --card-bg-color: #111111;
    --text-color: #ffffff;
    --hover-bg-color: #222222;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .link-card {
    animation: none;
  }
}

/* Print styles */
@media print {
  .header-controls,
  .search-container,
  nav {
    display: none;
  }

  .link-card {
    break-inside: avoid;
    page-break-inside: avoid;
  }

  body {
    background: white;
    color: black;
  }
}

/* Responsive Design */
@media (max-width: 1200px) {
  main {
    padding: 1rem;
    max-width: 95%;
  }

  .link-grid {
    grid-template-columns: repeat(auto-fill, minmax(8rem, 1fr));
    gap: 1rem;
  }
}

@media (max-width: 768px) {
  html {
    font-size: 15px;
  }

  main {
    padding: 0.8rem;
  }

  .link-grid {
    grid-template-columns: repeat(auto-fill, minmax(7rem, 1fr));
    gap: 0.8rem;
  }

  nav {
    padding: 0.4rem;
  }

  nav a {
    padding: 0.4rem 0.7rem;
    font-size: 0.95rem;
  }

  .category-title {
    font-size: 1.4rem;
    margin: 1.8rem 0 1rem;
  }

  .link-card {
    padding: 1rem 0.6rem;
    min-height: 80px;
  }

  .link-card i {
    font-size: 1.6rem;
    margin-bottom: 0.5rem;
  }

  .link-card h3 {
    font-size: 0.8rem;
    height: 2.4em;
  }
}

@media (max-width: 480px) {
  html {
    font-size: 14px;
  }

  main {
    padding: 0.6rem;
  }

  .link-grid {
    grid-template-columns: repeat(auto-fill, minmax(6rem, 1fr));
    gap: 0.7rem;
  }

  header h1 {
    font-size: 1.6rem;
  }

  nav li {
    margin: 0.25rem;
  }

  nav a {
    padding: 0.35rem 0.6rem;
    font-size: 0.9rem;
  }

  .category-title {
    font-size: 1.4rem;
    margin: 1.8rem 0 1rem;
  }

  .link-card {
    padding: 0.8rem 0.5rem;
    min-height: 70px;
  }

  .link-card i {
    font-size: 1.4rem;
    margin-bottom: 0.4rem;
  }

  .link-card h3 {
    font-size: 0.75rem;
    padding: 0 0.2rem;
    height: 2.4em;
  }

  footer {
    padding: 0.8rem;
  }

  footer nav a {
    margin: 0 0.4rem;
    font-size: 0.7rem;
  }
}

@media (max-width: 360px) {
  html {
    font-size: 13px;
  }

  .link-grid {
    grid-template-columns: repeat(auto-fill, minmax(5.5rem, 1fr));
    gap: 0.6rem;
  }

  header h1 {
    font-size: 1.4rem;
  }

  .link-card {
    padding: 0.8rem 0.5rem;
    min-height: 70px;
  }

  .link-card i {
    font-size: 1.3rem;
    margin-bottom: 0.3rem;
  }

  .link-card h3 {
    font-size: 0.7rem;
    padding: 0 0.15rem;
    height: 2.2em;
  }
}
