// Website navigation data structure
const navigationData = {
  categories: [
    {
      id: "ai-search",
      title: "Ai搜索",
      links: [
        { name: "Google", url: "https://www.google.com", icon: "fa-brands fa-google" },
        { name: "无限制AI", url: "https://unlimitedai.chat/", icon: "fa-solid fa-pepper-hot" },
        { name: "openrouter", url: "https://openrouter.ai/", icon: "fa-brands fa-openid" },
        { name: "websim", url: "https://websim.ai/", icon: "fa-solid fa-magnifying-glass" },
        { name: "chatgpt", url: "https://chatgpt.com/", icon: "fa-brands fa-google" },
        { name: "傻豆包", url: "https://www.doubao.com/chat/", icon: "fa-solid fa-paw" },
        { name: "扣子空间", url: "https://space.coze.cn/", icon: "fa-solid fa-shuttle-space" },
        { name: "通义千问", url: "https://chat.qwen.ai/", icon: "fa-brands fa-rocketchat" },
        { name: "kimi", url: "https://kimi.moonshot.cn/", icon: "fa-regular fa-moon" },
        { name: "文心一言", url: "https://yiyan.baidu.com/", icon: "fa-solid fa-dumpster-fire" },
        { name: "腾讯元宝", url: "https://yuanbao.tencent.com/", icon: "fa-solid fa-robot" },
        { name: "minimax", url: "https://www.minimax.io/", icon: "fa-solid fa-minimize" },
        { name: "z.ai", url: "https://chat.z.ai/", icon: "fa-solid fa-z" },
        { name: "google AIstudio", url: "https://aistudio.google.com/", icon: "fa-solid fa-robot" },
        { name: "gemini", url: "https://gemini.google.com/", icon: "fa-brands fa-google-plus" },
        { name: "labs.google", url: "https://labs.google/fx/zh", icon: "fa-brands fa-google-plus" },
        { name: "jules.google", url: "https://jules.google.com/", icon: "fa-brands fa-google-plus" },
        { name: "copilot", url: "https://copilot.microsoft.com/", icon: "fa-brands fa-windows" },
        { name: "nvidia", url: "https://build.nvidia.com/", icon: "fa-solid fa-microchip" },
        { name: "claude", url: "https://claude.ai/", icon: "fa-solid fa-robot" },
        { name: "chutesai", url: "https://chat.chutes.ai/", icon: "fa-solid fa-robot" },
        { name: "mistral", url: "https://mistral.ai/", icon: "fa-solid fa-brain" },
        { name: "groq", url: "https://groq.com/", icon: "fa-solid fa-group-arrows-rotate" },
        { name: "medscape", url: "https://www.medscape.com/", icon: "fa-solid fa-stethoscope" },
        { name: "heck.ai", url: "https://heck.ai/", icon: "fa-brands fa-rocketchat" },
        { name: "问小白", url: "https://www.wenxiaobai.com/", icon: "fa-solid fa-mask" },
        { name: "leonardo.ai绘图", url: "https://app.leonardo.ai/", icon: "fa-regular fa-images" },
        { name: "huggingface", url: "https://huggingface.co/", icon: "fa-solid fa-face-rolling-eyes" },
        { name: "lmarena", url: "https://lmarena.ai/", icon: "fa-solid fa-robot" },
        { name: "anyrouter", url: "https://anyrouter.top/", icon: "fa-solid fa-robot" },
        { name: "x.ai", url: "https://x.ai/", icon: "fa-brands fa-x-twitter" },
        { name: "pce计算", url: "https://ascvdpce.186404.xyz/", icon: "fa-solid fa-heart-pulse" },
        { name: "t3.chat", url: "https://t3.chat/", icon: "fa-brands fa-rocketchat" },
        { name: "v0", url: "https://v0.dev/", icon: "fa-solid fa-v" },
        { name: "deepseek", url: "https://www.deepseek.com/", icon: "fa-brands fa-rocketchat" },
        { name: "当贝AI", url: "https://ai.dangbei.com/", icon: "fa-brands fa-rocketchat" },
        { name: "genspark.ai", url: "https://www.genspark.ai/", icon: "fa-brands fa-rocketchat" },
        { name: "firebase.google", url: "https://firebase.google.com/", icon: "fa-solid fa-code-branch" },
        { name: "kiro.dev", url: "https://kiro.dev/", icon: "fa-brands fa-rocketchat" },
        { name: "sigmachat", url: "https://sigmabrowser.com/chat", icon: "fa-brands fa-rocketchat" },
        { name: "硅基流动", url: "https://siliconflow.cn/", icon: "fa-brands fa-confluence" },
        { name: "魔塔社区", url: "https://www.modelscope.cn/", icon: "fa-solid fa-code-branch" },
        { name: "readdy", url: "https://readdy.ai/", icon: "fa-solid fa-bolt" },
        { name: "bolt.new", url: "https://bolt.new/", icon: "fa-solid fa-bolt" }
      ]
    },
    {
      id: "social",
      title: "社交媒体",
      links: [
        { name: "Facebook", url: "https://www.facebook.com", icon: "fa-brands fa-facebook" },
        { name: "Twitter", url: "https://x.com/", icon: "fa-brands fa-twitter" },
        { name: "推特视频下载", url: "https://twitterxz.com/", icon: "fa-brands fa-x-twitter" },
        { name: "Instagram", url: "https://www.instagram.com", icon: "fa-brands fa-instagram" },
        { name: "render", url: "https://render.com/", icon: "fa-solid fa-draw-polygon" },
        { name: "deno", url: "https://deno.com/deploy", icon: "fa-solid fa-d" },
        { name: "railway", url: "https://railway.com/", icon: "fa-solid fa-archway" },
        { name: "vercel", url: "https://vercel.com/", icon: "fa-solid fa-v" },
        { name: "netlify", url: "https://www.netlify.com/", icon: "fa-brands fa-n" },
        { name: "savefrom", url: "https://zh.savefrom.net/", icon: "fa-solid fa-share-from-square" },
        { name: "爆米花视频", url: "https://goingbus.com/", icon: "fa-brands fa-n" },
        { name: "Reddit", url: "https://www.reddit.com", icon: "fa-brands fa-reddit" },
        { name: "油管字幕下载", url: "https://www.downloadyoutubesubtitles.com/", icon: "fa-brands fa-youtube" },
        { name: "GitHub", url: "https://github.com/", icon: "fa-brands fa-github" },
        { name: "油管视频下载", url: "https://www.y2mate.com/", icon: "fa-solid fa-download" },
        { name: "视频下载", url: "https://youtube.iiilab.com/", icon: "fa-solid fa-download" },
        { name: "suno", url: "https://suno.com/", icon: "fa-solid fa-music" },
        { name: "电影导航", url: "https://mv.186404.xyz/", icon: "fa-solid fa-tv" }
      ]
    },
    {
      id: "tools",
      title: "实用工具",
      links: [
        { name: "Google翻译", url: "https://translate.google.com", icon: "fa-solid fa-language" },
        { name: "短链", url: "https://d.186404.xyz/", icon: "fa-solid fa-link" },
        { name: "dynv6", url: "https://dynv6.com/", icon: "fa-solid fa-network-wired" },
        { name: "网速测试", url: "https://fast.com/", icon: "fa-solid fa-gauge-high" },
        { name: "Cloudns", url: "https://www.cloudns.net/", icon: "fa-solid fa-cloud" },
        { name: "Cloudflare", url: "https://www.cloudflare.com/zh-cn/", icon: "fa-solid fa-shield-halved" },
        { name: "一个朋友", url: "https://ygpy.net/", icon: "fa-solid fa-user-group" },
        { name: "谷歌笔记", url: "https://notebooklm.google/", icon: "fa-solid fa-book" },
        { name: "临时邮箱", url: "https://email.ml/", icon: "fa-solid fa-envelope" },
        { name: "A姐", url: "https://www.ahhhhfs.com/", icon: "fa-solid fa-blog" },
        { name: "IP查询", url: "https://ip.sb/", icon: "fa-solid fa-location-dot" },
        { name: "dns.he域名托管", url: "https://dns.he.net/", icon: "fa-solid fa-network-wired" },
        { name: "Site域名转发", url: "https://www.site.ac/", icon: "fa-solid fa-right-left" },
        { name: "Z-Library", url: "https://zh.go-to-library.sk/", icon: "fa-solid fa-book-reader" },
        { name: "us.kg域名", url: "https://domain.digitalplat.org/", icon: "fa-solid fa-globe" },
        { name: "Spaceship廉价域名", url: "https://www.spaceship.com/zh/", icon: "fa-solid fa-rocket" },
        { name: "cursor", url: "https://www.cursor.com/", icon: "fa-solid fa-i-cursor" },
        { name: "FontAwesome图标", url: "https://fontawesome.com/", icon: "fa-solid fa-icons" },
        { name: "ip清洁度查询", url: "https://scamalytics.com/", icon: "fa-solid fa-icons" },
        { name: "test-ipv6", url: "https://test-ipv6.com/", icon: "fa-solid fa-ethernet" },
        { name: "zone/ip", url: "https://html.zone/ip", icon: "fa-brands fa-sourcetree" },
        { name: "免费网络代理", url: "https://www.lumiproxy.com/zh-hans/online-proxy/proxysite/", icon: "fa-solid fa-unlock" },
        { name: "ipcheck", url: "https://ipcheck.ing/", icon: "fa-solid fa-location-dot" },
        { name: "定时任务cron-job", url: "https://console.cron-job.org/", icon: "fa-solid fa-ethernet" },
        { name: "uptimerobot", url: "https://uptimerobot.com/", icon: "fa-solid fa-location-dot" },
        { name: "forwardemail", url: "https://forwardemail.net/", icon: "fa-solid fa-envelopes-bulk" },
        { name: "improvmx", url: "https://improvmx.com/", icon: "fa-solid fa-envelopes-bulk" },
        { name: "github文件加速", url: "https://gb.w404.nyc.mn/", icon: "fa-brands fa-github" },
        { name: "hostryDNS域名托管", url: "https://hostry.com/", icon: "fa-solid fa-clock-rotate-left" },
        { name: "免费域名sitelutions", url: "https://www.sitelutions.com/", icon: "fa-solid fa-sitemap" },
        { name: "免费域名changeip", url: "https://www.changeip.com/", icon: "fa-solid fa-satellite-dish" },
        { name: "免费域名dnsexit", url: "https://dnsexit.com/", icon: "fa-solid fa-users" },
        { name: "免费域名gname", url: "https://www.gname.vip/", icon: "fa-solid fa-users" },
        { name: "DNS查找nslookup", url: "https://www.nslookup.io/", icon: "fa-solid fa-arrows-rotate" }
      ]
    },
    {
      id: "tech-news",
      title: "科技资讯",
      links: [
        { name: "TechCrunch", url: "https://www.techcrunch.com", icon: "fa-solid fa-newspaper" },
        { name: "Wired", url: "https://www.wired.com", icon: "fa-solid fa-bolt" },
        { name: "The Verge", url: "https://www.theverge.com", icon: "fa-solid fa-laptop" },
        { name: "Ars Technica", url: "https://arstechnica.com", icon: "fa-solid fa-rocket" },
        { name: "Engadget", url: "https://www.engadget.com", icon: "fa-solid fa-mobile-screen" },
        { name: "科技日报", url: "https://scitechdaily.com/", icon: "fa-solid fa-microscope" },
        { name: "TechRadar", url: "https://techradar.com", icon: "fa-solid fa-satellite" },
        { name: "科技博客", url: "https://b.186404.xyz/", icon: "fa-solid fa-blog" },
        { name: "cnbeta", url: "https://www.cnbeta.com.tw/", icon: "fa-solid fa-circle-info" }
      ]
    },
    {
      id: "cloud-storage",
      title: "云存储",
      links: [
        { name: "Dropbox", url: "https://www.dropbox.com", icon: "fa-brands fa-dropbox" },
        { name: "Google Drive", url: "https://drive.google.com", icon: "fa-brands fa-google-drive" },
        { name: "OneDrive", url: "https://onedrive.live.com", icon: "fa-brands fa-microsoft" },
        { name: "Box", url: "https://www.box.com", icon: "fa-solid fa-box" },
        { name: "MediaFire", url: "https://www.mediafire.com", icon: "fa-solid fa-file" },
        { name: "MEGA", url: "https://mega.nz", icon: "fa-solid fa-cloud-arrow-up" },
        { name: "PikPak", url: "https://mypikpak.com/", icon: "fa-solid fa-cloud" }
      ]
    },
    {
      id: "email",
      title: "电子邮箱",
      links: [
        { name: "Gmail", url: "https://mail.google.com", icon: "fa-solid fa-envelope" },
        { name: "Outlook", url: "https://outlook.live.com", icon: "fa-brands fa-microsoft" },
        { name: "cock邮箱", url: "https://cock.li/", icon: "fa-solid fa-envelope-open" },
        { name: "disroot邮箱", url: "https://disroot.org/", icon: "fa-solid fa-envelope-circle-check" },
        { name: "ProtonMail", url: "https://www.protonmail.com", icon: "fa-solid fa-shield-halved" },
        { name: "QQ邮箱", url: "https://mail.qq.com", icon: "fa-brands fa-qq" },
        { name: "librem邮箱", url: "https://librem.one/", icon: "fa-solid fa-at" },
        { name: "临时谷歌邮箱", url: "https://www.linshigmail.com/", icon: "fa-solid fa-envelopes-bulk" }
      ]
    }
  ]
};

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
  module.exports = navigationData;
}
