<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="description" content="WebNav Hub - 精选AI搜索、社交媒体、实用工具、科技资讯等优质网站导航，一站式访问您需要的所有网络资源。" />
  <meta name="keywords" content="网站导航,AI搜索,社交媒体,实用工具,科技资讯,云存储,电子邮箱" />
  <meta name="author" content="WebNav Hub" />
  <meta name="robots" content="index, follow" />

  <!-- Security headers -->
  <meta http-equiv="Content-Security-Policy"
    content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com; font-src 'self' https://cdnjs.cloudflare.com; img-src 'self' data: https:; connect-src 'self'; frame-ancestors 'none';" />
  <meta http-equiv="X-Content-Type-Options" content="nosniff" />
  <meta http-equiv="X-Frame-Options" content="DENY" />
  <meta http-equiv="X-XSS-Protection" content="1; mode=block" />
  <meta http-equiv="Referrer-Policy" content="strict-origin-when-cross-origin" />

  <!-- Performance optimizations -->
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <link rel="preconnect" href="https://cdnjs.cloudflare.com" />
  <link rel="dns-prefetch" href="https://cdnjs.cloudflare.com" />

  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website" />
  <meta property="og:title" content="WebNav Hub - 精选网站导航" />
  <meta property="og:description" content="精选AI搜索、社交媒体、实用工具等优质网站导航，一站式访问您需要的所有网络资源。" />
  <meta property="og:url" content="https://your-domain.com" />

  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image" />
  <meta property="twitter:title" content="WebNav Hub - 精选网站导航" />
  <meta property="twitter:description" content="精选AI搜索、社交媒体、实用工具等优质网站导航，一站式访问您需要的所有网络资源。" />

  <title>WebNav Hub - 精选网站导航 | AI搜索 社交媒体 实用工具</title>

  <!-- Critical CSS -->
  <link rel="stylesheet" href="styles.css" />

  <!-- Preload Font Awesome with optimized loading -->
  <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" as="style"
    onload="this.onload=null;this.rel='stylesheet'" />
  <noscript>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" />
  </noscript>

  <!-- Structured Data -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "WebNav Hub",
    "description": "精选AI搜索、社交媒体、实用工具、科技资讯等优质网站导航，一站式访问您需要的所有网络资源。",
    "url": "https://your-domain.com",
    "potentialAction": {
      "@type": "SearchAction",
      "target": "https://your-domain.com/?search={search_term_string}",
      "query-input": "required name=search_term_string"
    },
    "publisher": {
      "@type": "Organization",
      "name": "WebNav Hub"
    }
  }
  </script>

  <!-- JavaScript with defer for better performance -->
  <script src="script.js" defer></script>
</head>

<body>
  <!-- Skip to main content for accessibility -->
  <a href="#main-content" class="skip-link">跳转到主要内容</a>

  <header role="banner">
    <h1>WebNav Hub</h1>
    <div class="search-container" role="search">
      <label for="searchInput" class="visually-hidden">搜索网站</label>
      <input type="text" id="searchInput" placeholder="搜索网站..." autocomplete="off" />
      <button id="searchBtn" aria-label="搜索" type="button">
        <i class="fa-solid fa-search" aria-hidden="true"></i>
      </button>
    </div>

  </header>

  <nav role="navigation" aria-label="主导航">
    <ul>
      <!-- Navigation will be generated dynamically -->
    </ul>
  </nav>

  <main id="main-content" role="main">
    <!-- Main content will be generated dynamically -->
  </main>


  <!-- Back to top button -->
  <button id="backToTop" aria-label="返回顶部" type="button">
    <i class="fa-solid fa-arrow-up" aria-hidden="true"></i>
  </button>

  <footer role="contentinfo">
    <p>© 2024 WebNav Hub. 保留所有权利。</p>
    <nav aria-label="页脚导航">
      <a href="#" rel="noopener">隐私政策</a>
      <a href="#" rel="noopener">使用条款</a>
      <a href="#" rel="noopener">联系我们</a>
    </nav>
  </footer>
</body>

</html>